<template>
  <div class="document-reader" :class="{ 'reader-loading': loading }">
    <!-- 侧边栏 - 目录树 -->
    <div 
      v-if="showSidebar" 
      class="reader-sidebar" 
      :class="{ collapsed: sidebarCollapsed }"
    >
      <div class="sidebar-header">
        <h3 v-if="!sidebarCollapsed">目录</h3>
        <a-button
          type="text"
          size="small"
          @click="toggleSidebar"
          :icon="sidebarCollapsed ? 'MenuUnfoldOutlined' : 'MenuFoldOutlined'"
        />
      </div>
      
      <div v-if="!sidebarCollapsed" class="sidebar-content">
        <div class="catalog-tree">
          <div
            v-for="(item, index) in catalogItems"
            :key="`catalog-${index}`"
            class="catalog-item"
            :class="[
              `level-${item.hierarchy}`,
              { active: activeCatalogId === `${item.page_id}-${index}` }
            ]"
            @click="handleCatalogClick(item, index)"
          >
            <span class="catalog-title">{{ item.title }}</span>
            <span class="catalog-page">第{{ item.page_id }}页</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="reader-main">
      <!-- 工具栏 -->
      <div class="reader-toolbar">
        <div class="toolbar-left">
          <a-button-group size="small">
            <a-button @click="zoomOut" :disabled="currentZoom <= minZoom">
              <template #icon><ZoomOutOutlined /></template>
            </a-button>
            <a-button class="zoom-display">{{ Math.round(currentZoom * 100) }}%</a-button>
            <a-button @click="zoomIn" :disabled="currentZoom >= maxZoom">
              <template #icon><ZoomInOutlined /></template>
            </a-button>
            <a-button @click="resetZoom">
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </a-button-group>
        </div>
        
        <div class="toolbar-center">
          <a-input-group compact>
            <a-input
              v-model:value="currentPageInput"
              size="small"
              style="width: 60px"
              @pressEnter="goToPage"
              @blur="goToPage"
            />
            <a-button size="small" disabled>/ {{ totalPages }}</a-button>
          </a-input-group>
        </div>
        
        <div class="toolbar-right">
          <a-button-group size="small">
            <a-button @click="previousPage" :disabled="currentPage <= 1">
              <template #icon><LeftOutlined /></template>
            </a-button>
            <a-button @click="nextPage" :disabled="currentPage >= totalPages">
              <template #icon><RightOutlined /></template>
            </a-button>
          </a-button-group>
        </div>
      </div>

      <!-- 文档内容区域 -->
      <div 
        ref="documentContainer"
        class="document-container"
        @scroll="handleScroll"
      >
        <div 
          class="document-content"
          :style="{ transform: `scale(${currentZoom})`, transformOrigin: 'top center' }"
        >
          <!-- 虚拟滚动容器 -->
          <div 
            class="virtual-scroll-container"
            :style="{ height: `${totalHeight}px` }"
          >
            <!-- 渲染可见页面 -->
            <div
              v-for="page in visiblePages"
              :key="`page-${page.page_id}`"
              class="document-page"
              :style="{ 
                position: 'absolute',
                top: `${getPageOffset(page.page_id)}px`,
                width: '100%'
              }"
            >
              <DocumentPage
                :page="page"
                :highlights="getPageHighlights(page.page_id)"
                :suggestions="getPageSuggestions(page.page_id)"
                :scale="currentZoom"
                @suggestion-click="handleSuggestionClick"
                @text-select="handleTextSelect"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修订建议面板 -->
    <div 
      v-if="showRevisionPanel" 
      class="reader-revision-panel"
      :class="{ collapsed: revisionPanelCollapsed }"
    >
      <div class="revision-header">
        <h3 v-if="!revisionPanelCollapsed">修订建议</h3>
        <a-button
          type="text"
          size="small"
          @click="toggleRevisionPanel"
          :icon="revisionPanelCollapsed ? 'MenuUnfoldOutlined' : 'MenuFoldOutlined'"
        />
      </div>
      
      <div v-if="!revisionPanelCollapsed" class="revision-content">
        <RevisionPanel
          :suggestions="suggestions || []"
          :active-suggestion-id="activeSuggestionId"
          @suggestion-click="handleSuggestionClick"
          @suggestion-accept="handleSuggestionAccept"
          @suggestion-reject="handleSuggestionReject"
        />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="reader-loading-overlay">
      <a-spin size="large" tip="正在加载文档..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons-vue'
import DocumentPage from './DocumentPage.vue'
import RevisionPanel from './RevisionPanel.vue'
import type { 
  DocumentReaderProps, 
  DocumentReaderEmits,
  DocumentPage as DocumentPageType,
  CatalogItem,
  RevisionSuggestion,
  HighlightArea,
  TextSelection,
  ViewportInfo
} from './types'

defineOptions({
  name: 'DocumentReader'
})

// Props 和 Emits
const props = withDefaults(defineProps<DocumentReaderProps>(), {
  loading: false,
  showSidebar: true,
  showRevisionPanel: true,
  readonly: false
})

const emit = defineEmits<DocumentReaderEmits>()

// 响应式数据
const documentContainer = ref<HTMLElement>()
const sidebarCollapsed = ref(false)
const revisionPanelCollapsed = ref(false)

// 缩放相关
const currentZoom = ref(1)
const minZoom = 0.5
const maxZoom = 3
const zoomStep = 0.1

// 页面导航相关
const currentPage = ref(1)
const currentPageInput = ref('1')

// 目录相关
const activeCatalogId = ref('')
const activeSuggestionId = ref('')

// 虚拟滚动相关
const scrollTop = ref(0)
const containerHeight = ref(800)
const pageHeight = 1000 // 估算的页面高度
const bufferSize = 2 // 缓冲页面数量

// 计算属性
const catalogItems = computed(() => {
  if (!props.data?.catalog?.toc) return []
  return props.data.catalog.toc.flat()
})

const totalPages = computed(() => {
  return props.data?.pages?.length || 0
})

const totalHeight = computed(() => {
  return totalPages.value * pageHeight
})

const visiblePages = computed(() => {
  if (!props.data?.pages) return []
  
  const startIndex = Math.max(0, Math.floor(scrollTop.value / pageHeight) - bufferSize)
  const endIndex = Math.min(
    totalPages.value - 1,
    Math.ceil((scrollTop.value + containerHeight.value) / pageHeight) + bufferSize
  )
  
  return props.data.pages.slice(startIndex, endIndex + 1)
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleRevisionPanel = () => {
  revisionPanelCollapsed.value = !revisionPanelCollapsed.value
}

const zoomIn = () => {
  if (currentZoom.value < maxZoom) {
    currentZoom.value = Math.min(maxZoom, currentZoom.value + zoomStep)
  }
}

const zoomOut = () => {
  if (currentZoom.value > minZoom) {
    currentZoom.value = Math.max(minZoom, currentZoom.value - zoomStep)
  }
}

const resetZoom = () => {
  currentZoom.value = 1
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    scrollToPage(currentPage.value)
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    scrollToPage(currentPage.value)
  }
}

const goToPage = () => {
  const pageNum = parseInt(currentPageInput.value)
  if (pageNum >= 1 && pageNum <= totalPages.value) {
    currentPage.value = pageNum
    scrollToPage(pageNum)
  } else {
    currentPageInput.value = currentPage.value.toString()
  }
}

const scrollToPage = (pageNum: number) => {
  if (documentContainer.value) {
    const targetScrollTop = (pageNum - 1) * pageHeight
    documentContainer.value.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    })
  }
}

const getPageOffset = (pageId: number) => {
  return (pageId - 1) * pageHeight
}

const getPageHighlights = (pageId: number): HighlightArea[] => {
  // 根据页面ID获取高亮区域
  return []
}

const getPageSuggestions = (pageId: number): RevisionSuggestion[] => {
  if (!props.suggestions) return []
  return props.suggestions.filter(s => s.pageId === pageId)
}

const handleScroll = () => {
  if (documentContainer.value) {
    scrollTop.value = documentContainer.value.scrollTop
    
    // 更新当前页面
    const newCurrentPage = Math.floor(scrollTop.value / pageHeight) + 1
    if (newCurrentPage !== currentPage.value && newCurrentPage <= totalPages.value) {
      currentPage.value = newCurrentPage
      currentPageInput.value = newCurrentPage.toString()
      emit('page-change', newCurrentPage)
    }
  }
}

const handleCatalogClick = (item: CatalogItem, index: number) => {
  activeCatalogId.value = `${item.page_id}-${index}`
  scrollToPage(item.page_id)
  emit('catalog-click', item)
}

const handleSuggestionClick = (suggestion: RevisionSuggestion) => {
  activeSuggestionId.value = suggestion.id
  scrollToPage(suggestion.pageId)
  emit('suggestion-click', suggestion)
}

const handleSuggestionAccept = (suggestion: RevisionSuggestion) => {
  emit('suggestion-accept', suggestion)
}

const handleSuggestionReject = (suggestion: RevisionSuggestion) => {
  emit('suggestion-reject', suggestion)
}

const handleTextSelect = (selection: TextSelection) => {
  emit('text-select', selection)
}

// 监听器
watch(() => currentPage.value, (newPage) => {
  currentPageInput.value = newPage.toString()
})

// 生命周期
onMounted(() => {
  if (documentContainer.value) {
    containerHeight.value = documentContainer.value.clientHeight
  }
})
</script>

<style lang="scss" scoped>
.document-reader {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
  position: relative;

  &.reader-loading {
    pointer-events: none;
  }
}

.reader-sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  z-index: 10;

  &.collapsed {
    width: 48px;
  }

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .sidebar-content {
    flex: 1;
    overflow: hidden;
  }

  .catalog-tree {
    height: 100%;
    overflow-y: auto;
    padding: 8px 0;

    .catalog-item {
      padding: 8px 16px;
      cursor: pointer;
      border-left: 3px solid transparent;
      transition: all 0.2s ease;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:hover {
        background: #f0f0f0;
      }

      &.active {
        background: #e6f7ff;
        border-left-color: #1890ff;
        color: #1890ff;
      }

      &.level-0 {
        font-weight: 600;
        font-size: 16px;
        color: #1890ff;
      }

      &.level-1 {
        padding-left: 24px;
        font-weight: 500;
        font-size: 14px;
      }

      &.level-2 {
        padding-left: 32px;
        font-size: 13px;
      }

      .catalog-title {
        flex: 1;
        line-height: 1.4;
      }

      .catalog-page {
        font-size: 12px;
        color: #666;
        margin-left: 8px;
      }
    }
  }
}

.reader-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.reader-toolbar {
  height: 48px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 5;

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .zoom-display {
    min-width: 60px;
    text-align: center;
    cursor: default;
  }
}

.document-container {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
}

.document-content {
  min-height: 100%;
  transition: transform 0.2s ease;
}

.virtual-scroll-container {
  position: relative;
  width: 100%;
}

.document-page {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 20px;
}

.reader-revision-panel {
  width: 400px;
  background: white;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  z-index: 10;

  &.collapsed {
    width: 48px;
  }

  .revision-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .revision-content {
    flex: 1;
    overflow: hidden;
  }
}

.reader-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// 响应式设计
@media (max-width: 1200px) {
  .reader-sidebar {
    width: 250px;

    &.collapsed {
      width: 48px;
    }
  }

  .reader-revision-panel {
    width: 320px;

    &.collapsed {
      width: 48px;
    }
  }
}

@media (max-width: 768px) {
  .document-reader {
    .reader-sidebar,
    .reader-revision-panel {
      position: fixed;
      top: 0;
      height: 100vh;
      z-index: 100;
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &:not(.collapsed) {
        transform: translateX(0);
      }
    }

    .reader-revision-panel {
      right: 0;
      transform: translateX(100%);

      &:not(.collapsed) {
        transform: translateX(0);
      }
    }

    .reader-main {
      margin: 0;
    }
  }

  .reader-toolbar {
    padding: 0 8px;

    .toolbar-center {
      order: -1;
    }
  }
}

// 打印样式
@media print {
  .document-reader {
    .reader-sidebar,
    .reader-toolbar,
    .reader-revision-panel {
      display: none;
    }

    .reader-main {
      margin: 0;
    }

    .document-container {
      overflow: visible;
    }

    .document-page {
      page-break-inside: avoid;
      margin: 0;
      padding: 0;
    }
  }
}
</style>
