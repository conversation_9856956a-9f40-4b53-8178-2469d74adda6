<template>
    <div class="document-reader-container">
        <!-- 目录导航 -->
        <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
            <div class="sidebar-header">
                <h3 v-if="!sidebarCollapsed">目录</h3>
                <a-button
                    type="text"
                    size="small"
                    @click="sidebarCollapsed = !sidebarCollapsed"
                >
                    <template #icon>
                        <MenuOutlined v-if="sidebarCollapsed" />
                        <MenuFoldOutlined v-else />
                    </template>
                </a-button>
            </div>
            
            <div v-if="!sidebarCollapsed" class="toc-container">
                <div class="toc-search">
                    <a-input
                        v-model:value="tocSearchText"
                        placeholder="搜索目录..."
                        size="small"
                        allow-clear
                    >
                        <template #prefix>
                            <SearchOutlined />
                        </template>
                    </a-input>
                </div>
                
                <div class="toc-list">
                    <div
                        v-for="item in filteredTocItems"
                        :key="`${item.page_id}-${item.hierarchy}`"
                        class="toc-item"
                        :class="[
                            `level-${item.hierarchy}`,
                            { active: currentPageId === item.page_id }
                        ]"
                        @click="jumpToPage(item.page_id, item.pos)"
                    >
                        <div class="toc-title">{{ item.title }}</div>
                        <div class="toc-page">第{{ item.page_id }}页</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <a-button-group size="small">
                        <a-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
                            <template #icon><ZoomOutOutlined /></template>
                        </a-button>
                        <a-button @click="resetZoom">
                            {{ Math.round(zoomLevel * 100) }}%
                        </a-button>
                        <a-button @click="zoomIn" :disabled="zoomLevel >= 3">
                            <template #icon><ZoomInOutlined /></template>
                        </a-button>
                    </a-button-group>
                    
                    <a-divider type="vertical" />
                    
                    <div class="page-navigation">
                        <a-button-group size="small">
                            <a-button @click="prevPage" :disabled="currentPageId <= 1">
                                <template #icon><LeftOutlined /></template>
                            </a-button>
                            <a-input-number
                                v-model:value="currentPageId"
                                :min="1"
                                :max="totalPages"
                                size="small"
                                style="width: 80px"
                                @change="jumpToPage"
                            />
                            <a-button @click="nextPage" :disabled="currentPageId >= totalPages">
                                <template #icon><RightOutlined /></template>
                            </a-button>
                        </a-button-group>
                        <span class="page-total">/ {{ totalPages }}</span>
                    </div>
                </div>
                
                <div class="toolbar-right">
                    <a-button
                        type="text"
                        size="small"
                        @click="toggleFullscreen"
                        title="全屏"
                    >
                        <template #icon>
                            <FullscreenExitOutlined v-if="isFullscreen" />
                            <FullscreenOutlined v-else />
                        </template>
                    </a-button>
                </div>
            </div>
            
            <!-- 文档内容区域 -->
            <div class="document-viewer" ref="documentViewerRef">
                <a-spin :spinning="loading" tip="加载中...">
                    <div 
                        class="document-container"
                        :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }"
                    >
                        <div
                            v-for="page in visiblePages"
                            :key="page.page_id"
                            class="page-container"
                            :class="{ 'current-page': currentPageId === page.page_id }"
                            :data-page-id="page.page_id"
                            ref="pageRefs"
                        >
                            <div class="page-header">
                                <span class="page-number">第 {{ page.page_id }} 页</span>
                            </div>
                            
                            <div class="page-content" :style="getPageStyle(page)">
                                <!-- 页面背景图片 -->
                                <img
                                    v-if="page.base64"
                                    :src="`data:image/jpeg;base64,${page.base64}`"
                                    class="page-background"
                                    :style="getPageStyle(page)"
                                    @load="onPageImageLoad(page.page_id)"
                                />
                                
                                <!-- 文本内容层 -->
                                <div class="text-layer">
                                    <div
                                        v-for="content in getPageContent(page.page_id)"
                                        :key="`${content.page_id}-${content.paragraph_id}`"
                                        class="content-item"
                                        :class="getContentClasses(content)"
                                        :style="getContentStyle(content)"
                                        @click="selectContent(content)"
                                    >
                                        <!-- 普通段落 -->
                                        <div v-if="content.type === 'paragraph'" class="paragraph-content">
                                            {{ content.text }}
                                        </div>
                                        
                                        <!-- 表格内容 -->
                                        <div v-else-if="content.type === 'table'" class="table-content">
                                            <DocumentTable
                                                v-if="content.cells?.length"
                                                :cells="content.cells"
                                                :cross-page="content.split_section_page_ids?.length > 1"
                                            />
                                            <div v-else class="table-placeholder">
                                                {{ content.text || '表格内容' }}
                                            </div>
                                        </div>
                                        
                                        <!-- 图片内容 -->
                                        <div v-else-if="content.type === 'image'" class="image-content">
                                            <img
                                                v-if="content.image_url"
                                                :src="content.image_url"
                                                class="content-image"
                                                @load="onContentImageLoad"
                                            />
                                            <div v-else class="image-placeholder">
                                                {{ content.text || '图片内容' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 高亮遮罩层 -->
                                <div class="highlight-layer">
                                    <div
                                        v-for="highlight in getPageHighlights(page.page_id)"
                                        :key="highlight.id"
                                        class="highlight-overlay"
                                        :style="getHighlightStyle(highlight)"
                                        :class="`highlight-${highlight.type}`"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a-spin>
            </div>
        </div>

        <!-- 修订建议面板 -->
        <div class="revision-panel" :class="{ collapsed: revisionPanelCollapsed }" v-if="showRevisionPanel">
            <div class="revision-header">
                <h3>修订建议</h3>
                <div class="header-actions">
                    <a-button
                        type="text"
                        size="small"
                        @click="clearAllHighlights"
                        title="清除高亮"
                    >
                        <template #icon>
                            <ClearOutlined />
                        </template>
                    </a-button>
                    <a-button
                        type="text"
                        size="small"
                        @click="revisionPanelCollapsed = !revisionPanelCollapsed"
                        title="收起面板"
                    >
                        <template #icon>
                            <RightOutlined v-if="revisionPanelCollapsed" />
                            <LeftOutlined v-else />
                        </template>
                    </a-button>
                </div>
            </div>

            <RevisionPanel
                v-if="!revisionPanelCollapsed"
                :suggestions="revisionSuggestions"
                :active-suggestion-id="activeSuggestionId"
                @accept-suggestion="acceptSuggestion"
                @reject-suggestion="rejectSuggestion"
                @jump-to-suggestion="jumpToSuggestion"
                @add-suggestion="addSuggestion"
            />
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import {
    MenuOutlined,
    MenuFoldOutlined,
    SearchOutlined,
    ZoomInOutlined,
    ZoomOutOutlined,
    LeftOutlined,
    RightOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    ClearOutlined
} from '@ant-design/icons-vue'
import DocumentTable from './components/DocumentTable.vue'
import RevisionPanel from './components/RevisionPanel.vue'
import type { 
    DocumentReaderProps, 
    DocumentItem, 
    PageData, 
    TocItem, 
    RevisionSuggestion, 
    HighlightItem 
} from './types'

defineOptions({
    name: 'DocumentReader'
})

// Props定义
const props = withDefaults(defineProps<DocumentReaderProps>(), {
    showRevisionPanel: true,
    enableEdit: true,
    initialZoom: 1,
    virtualScroll: true
})

// Emits定义
const emits = defineEmits<{
    pageChange: [pageId: number]
    contentSelect: [content: DocumentItem]
    suggestionAccept: [suggestion: RevisionSuggestion]
    suggestionReject: [suggestion: RevisionSuggestion]
    zoomChange: [zoom: number]
}>()

// 响应式数据
const documentData = ref<DocumentItem[]>([])
const pagesData = ref<PageData[]>([])
const tocData = ref<TocItem[]>([])
const loading = ref(false)

// 布局相关
const sidebarCollapsed = ref(false)
const revisionPanelCollapsed = ref(false)
const documentViewerRef = ref<HTMLElement>()
const pageRefs = ref<HTMLElement[]>([])

// 缩放和导航
const zoomLevel = ref(props.initialZoom)
const currentPageId = ref(1)
const totalPages = ref(0)
const isFullscreen = ref(false)

// 目录搜索
const tocSearchText = ref('')
const filteredTocItems = computed(() => {
    if (!tocSearchText.value.trim()) {
        return tocData.value
    }
    return tocData.value.filter(item =>
        item.title.toLowerCase().includes(tocSearchText.value.toLowerCase())
    )
})

// 虚拟滚动优化 - 只渲染可见页面
const visiblePages = computed(() => {
    if (!props.virtualScroll) {
        return pagesData.value
    }
    
    // 渲染当前页面及前后各2页
    const start = Math.max(1, currentPageId.value - 2)
    const end = Math.min(totalPages.value, currentPageId.value + 2)
    return pagesData.value.filter(page => 
        page.page_id >= start && page.page_id <= end
    )
})

// 高亮相关
const highlightedItems = ref(new Set<string>())
const highlights = ref<HighlightItem[]>([])
const activeSuggestionId = ref<string>('')
const revisionSuggestions = ref<RevisionSuggestion[]>([])

// 监听props变化
watch(() => props.documentData, (newData) => {
    if (newData) {
        loadDocumentData(newData)
    }
}, { immediate: true })

watch(() => props.revisionSuggestions, (newSuggestions) => {
    if (newSuggestions) {
        revisionSuggestions.value = newSuggestions
    }
}, { immediate: true })

// 缩放控制
const zoomIn = () => {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.25)
    emits('zoomChange', zoomLevel.value)
}

const zoomOut = () => {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.25)
    emits('zoomChange', zoomLevel.value)
}

const resetZoom = () => {
    zoomLevel.value = 1
    emits('zoomChange', zoomLevel.value)
}

// 页面导航
const jumpToPage = (pageId: number, position?: number[]) => {
    currentPageId.value = pageId
    emits('pageChange', pageId)

    nextTick(() => {
        const pageElement = document.querySelector(`[data-page-id="${pageId}"]`)
        if (pageElement) {
            pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })

            // 如果有具体位置，进一步滚动到该位置
            if (position && position.length >= 4) {
                const [, y1] = position
                const rect = pageElement.getBoundingClientRect()
                const targetY = rect.top + y1 * zoomLevel.value
                window.scrollTo({
                    top: targetY - 100,
                    behavior: 'smooth'
                })
            }
        }
    })
}

const prevPage = () => {
    if (currentPageId.value > 1) {
        jumpToPage(currentPageId.value - 1)
    }
}

const nextPage = () => {
    if (currentPageId.value < totalPages.value) {
        jumpToPage(currentPageId.value + 1)
    }
}

// 全屏控制
const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
        documentViewerRef.value?.requestFullscreen()
        isFullscreen.value = true
    } else {
        document.exitFullscreen()
        isFullscreen.value = false
    }
}

// 内容获取和样式计算
const getPageContent = (pageId: number) => {
    return documentData.value.filter(item => item.page_id === pageId)
}

const getPageStyle = (page: PageData) => {
    return {
        width: `${page.width}px`,
        height: `${page.height}px`
    }
}

const getContentStyle = (content: DocumentItem) => {
    if (!content.position || content.position.length < 4) return {}

    const [x1, y1, x2, y2] = content.position
    return {
        position: 'absolute' as const,
        left: `${x1}px`,
        top: `${y1}px`,
        width: `${x2 - x1}px`,
        height: `${y2 - y1}px`,
        fontSize: `${Math.max(12, (y2 - y1) * 0.8)}px`,
        lineHeight: '1.2'
    }
}

const getContentClasses = (content: DocumentItem) => {
    return [
        `type-${content.type}`,
        `subtype-${content.sub_type || 'default'}`,
        {
            highlighted: highlightedItems.value.has(`${content.page_id}-${content.paragraph_id}`),
            'cross-page': content.split_section_page_ids?.length > 1
        }
    ]
}

// 高亮和选择
const selectContent = (content: DocumentItem) => {
    const key = `${content.page_id}-${content.paragraph_id}`
    if (highlightedItems.value.has(key)) {
        highlightedItems.value.delete(key)
    } else {
        highlightedItems.value.add(key)
    }
    emits('contentSelect', content)
}

const getPageHighlights = (pageId: number) => {
    return highlights.value.filter(h => h.pageId === pageId)
}

const getHighlightStyle = (highlight: HighlightItem) => {
    return {
        position: 'absolute' as const,
        left: `${highlight.position.x1}px`,
        top: `${highlight.position.y1}px`,
        width: `${highlight.position.x2 - highlight.position.x1}px`,
        height: `${highlight.position.y2 - highlight.position.y1}px`,
        backgroundColor: highlight.color || 'rgba(255, 255, 0, 0.3)',
        pointerEvents: 'none' as const
    }
}

// 图片加载处理
const onPageImageLoad = (pageId: number) => {
    console.log(`页面 ${pageId} 图片加载完成`)
}

const onContentImageLoad = () => {
    console.log('内容图片加载完成')
}

// 建议处理
const jumpToSuggestion = (suggestion: RevisionSuggestion) => {
    activeSuggestionId.value = suggestion.id
    jumpToPage(suggestion.pageId)

    // 添加高亮
    const highlightId = `suggestion-${suggestion.id}`
    const existingIndex = highlights.value.findIndex(h => h.id === highlightId)

    const newHighlight: HighlightItem = {
        id: highlightId,
        type: 'suggestion',
        pageId: suggestion.pageId,
        position: suggestion.position,
        color: 'rgba(255, 165, 0, 0.4)'
    }

    if (existingIndex >= 0) {
        highlights.value[existingIndex] = newHighlight
    } else {
        highlights.value.push(newHighlight)
    }
}

const acceptSuggestion = (suggestion: RevisionSuggestion) => {
    emits('suggestionAccept', suggestion)

    // 移除高亮
    const highlightIndex = highlights.value.findIndex(h => h.id === `suggestion-${suggestion.id}`)
    if (highlightIndex >= 0) {
        highlights.value.splice(highlightIndex, 1)
    }
}

const rejectSuggestion = (suggestion: RevisionSuggestion) => {
    emits('suggestionReject', suggestion)

    // 移除高亮
    const highlightIndex = highlights.value.findIndex(h => h.id === `suggestion-${suggestion.id}`)
    if (highlightIndex >= 0) {
        highlights.value.splice(highlightIndex, 1)
    }
}

const addSuggestion = (suggestionData: any) => {
    // 由父组件处理建议添加逻辑
    console.log('添加建议:', suggestionData)
}

// 清除所有高亮
const clearAllHighlights = () => {
    highlightedItems.value.clear()
    highlights.value = []
    activeSuggestionId.value = ''
}

// 数据加载
const loadDocumentData = (data: any) => {
    loading.value = true

    try {
        if (data.detail) {
            documentData.value = data.detail
        }

        if (data.pages) {
            pagesData.value = data.pages
            totalPages.value = data.total_page_number || data.pages.length
        }

        if (data.catalog?.toc) {
            tocData.value = data.catalog.toc.flat()
        }
    } catch (error) {
        console.error('加载文档数据异常:', error)
    } finally {
        loading.value = false
    }
}

// 暴露方法给父组件
defineExpose({
    jumpToPage,
    zoomIn,
    zoomOut,
    resetZoom,
    clearAllHighlights,
    jumpToSuggestion
})
</script>

<style lang="scss" scoped>
.document-reader-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    .sidebar {
        width: 300px;
        background: white;
        border-right: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        transition: width 0.3s ease;
        z-index: 10;

        &.collapsed {
            width: 48px;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #262626;
            }
        }

        .toc-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .toc-search {
                padding: 12px 16px;
                border-bottom: 1px solid #f0f0f0;
            }

            .toc-list {
                flex: 1;
                overflow-y: auto;
                padding: 8px 0;

                .toc-item {
                    padding: 8px 16px;
                    cursor: pointer;
                    border-left: 3px solid transparent;
                    transition: all 0.2s ease;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    &:hover {
                        background: #f0f0f0;
                    }

                    &.active {
                        background: #e6f7ff;
                        border-left-color: #1890ff;

                        .toc-title {
                            color: #1890ff;
                        }
                    }

                    .toc-title {
                        font-size: 14px;
                        line-height: 1.4;
                        flex: 1;
                        margin-right: 8px;
                    }

                    .toc-page {
                        font-size: 12px;
                        color: #999;
                        white-space: nowrap;
                    }

                    &.level-1 {
                        padding-left: 24px;
                        font-size: 13px;
                    }

                    &.level-2 {
                        padding-left: 32px;
                        font-size: 12px;
                    }

                    &.level-3 {
                        padding-left: 40px;
                        font-size: 12px;
                    }
                }
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .toolbar {
            height: 48px;
            background: white;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            z-index: 5;

            .toolbar-left {
                display: flex;
                align-items: center;
                gap: 16px;

                .page-navigation {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .page-total {
                        font-size: 14px;
                        color: #666;
                        margin-left: 4px;
                    }
                }
            }
        }

        .document-viewer {
            flex: 1;
            overflow: auto;
            background: #f5f5f5;
            position: relative;

            .document-container {
                min-height: 100%;
                padding: 20px;
                transform-origin: top left;
                transition: transform 0.2s ease;

                .page-container {
                    background: white;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    border-radius: 4px;
                    overflow: hidden;
                    position: relative;

                    &.current-page {
                        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
                        border: 2px solid #1890ff;
                    }

                    .page-header {
                        background: #fafafa;
                        padding: 8px 16px;
                        border-bottom: 1px solid #e8e8e8;
                        font-size: 12px;
                        color: #666;
                    }

                    .page-content {
                        position: relative;
                        margin: 0 auto;

                        .page-background {
                            display: block;
                            max-width: 100%;
                            height: auto;
                        }

                        .text-layer {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            pointer-events: none;

                            .content-item {
                                pointer-events: auto;
                                cursor: pointer;
                                transition: all 0.2s ease;
                                border-radius: 2px;

                                &:hover {
                                    background: rgba(24, 144, 255, 0.1);
                                }

                                &.highlighted {
                                    background: rgba(255, 255, 0, 0.3);
                                    border: 1px solid #faad14;
                                }

                                &.cross-page {
                                    border: 2px dashed #ff7875;
                                    background: rgba(255, 120, 117, 0.1);
                                }

                                .paragraph-content {
                                    padding: 2px 4px;
                                    font-size: inherit;
                                    line-height: inherit;
                                    word-break: break-word;
                                }

                                .table-content {
                                    .table-placeholder {
                                        padding: 8px;
                                        background: rgba(0, 0, 0, 0.05);
                                        border: 1px dashed #d9d9d9;
                                        text-align: center;
                                        color: #666;
                                        font-size: 12px;
                                    }
                                }

                                .image-content {
                                    .content-image {
                                        max-width: 100%;
                                        height: auto;
                                    }

                                    .image-placeholder {
                                        padding: 8px;
                                        background: rgba(0, 0, 0, 0.05);
                                        border: 1px dashed #d9d9d9;
                                        text-align: center;
                                        color: #666;
                                        font-size: 12px;
                                    }
                                }
                            }
                        }

                        .highlight-layer {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            pointer-events: none;

                            .highlight-overlay {
                                border-radius: 2px;
                                transition: all 0.2s ease;

                                &.highlight-suggestion {
                                    background: rgba(255, 165, 0, 0.4);
                                    border: 1px solid #fa8c16;
                                }

                                &.highlight-search {
                                    background: rgba(255, 255, 0, 0.4);
                                    border: 1px solid #fadb14;
                                }

                                &.highlight-selection {
                                    background: rgba(24, 144, 255, 0.3);
                                    border: 1px solid #1890ff;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .revision-panel {
        width: 400px;
        background: white;
        border-left: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        transition: width 0.3s ease;
        z-index: 10;

        &.collapsed {
            width: 0;
            overflow: hidden;
        }

        .revision-header {
            padding: 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #262626;
            }

            .header-actions {
                display: flex;
                gap: 4px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .document-reader-container {
        .sidebar {
            width: 250px;

            &.collapsed {
                width: 48px;
            }
        }

        .revision-panel {
            width: 320px;
        }
    }
}

@media (max-width: 768px) {
    .document-reader-container {
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(-100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .revision-panel {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .main-content {
            margin-left: 0;
        }

        .document-container {
            padding: 10px;

            .page-container {
                margin-bottom: 10px;
            }
        }
    }
}

// 打印样式
@media print {
    .document-reader-container {
        .sidebar,
        .toolbar,
        .revision-panel {
            display: none;
        }

        .main-content {
            margin: 0;
        }

        .document-container {
            transform: none !important;
            padding: 0;

            .page-container {
                box-shadow: none;
                margin: 0;
                page-break-inside: avoid;

                .page-header {
                    display: none;
                }
            }
        }

        .highlight-overlay {
            display: none;
        }
    }
}

// 高性能优化
.document-viewer {
    // 启用硬件加速
    transform: translateZ(0);
    will-change: scroll-position;
}

.page-container {
    // 优化重绘性能
    contain: layout style paint;
}

.text-layer {
    // 优化文本渲染
    text-rendering: optimizeSpeed;
}

// 加载动画
.ant-spin-nested-loading {
    .ant-spin {
        max-height: none;
    }
}
</style>
