<template>
    <div class="md-reader-container">
        <!-- 目录导航 -->
        <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
            <div class="sidebar-header">
                <h3 v-if="!sidebarCollapsed">目录</h3>
                <a-button
                    type="text"
                    size="small"
                    @click="sidebarCollapsed = !sidebarCollapsed"
                >
                    <template #icon>
                        <MenuOutlined v-if="sidebarCollapsed" />
                        <MenuFoldOutlined v-else />
                    </template>
                </a-button>
            </div>

            <div v-if="!sidebarCollapsed" class="toc-container">
                <div class="toc-search">
                    <a-input
                        v-model:value="tocSearchText"
                        placeholder="搜索目录..."
                        size="small"
                        allow-clear
                    >
                        <template #prefix>
                            <SearchOutlined />
                        </template>
                    </a-input>
                </div>

                <div class="toc-list">
                    <div
                        v-for="item in filteredTocItems"
                        :key="`${item.page_id}-${item.hierarchy}`"
                        class="toc-item"
                        :class="[
                            `level-${item.hierarchy}`,
                            { active: currentPageId === item.page_id }
                        ]"
                        @click="jumpToPage(item.page_id, item.pos)"
                    >
                        <div class="toc-title">{{ item.title }}</div>
                        <div class="toc-page">第{{ item.page_id }}页</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <a-button-group size="small">
                        <a-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
                            <template #icon><ZoomOutOutlined /></template>
                        </a-button>
                        <a-button @click="resetZoom">
                            {{ Math.round(zoomLevel * 100) }}%
                        </a-button>
                        <a-button @click="zoomIn" :disabled="zoomLevel >= 3">
                            <template #icon><ZoomInOutlined /></template>
                        </a-button>
                    </a-button-group>

                    <a-divider type="vertical" />

                    <div class="page-navigation">
                        <a-button-group size="small">
                            <a-button @click="prevPage" :disabled="currentPageId <= 1">
                                <template #icon><LeftOutlined /></template>
                            </a-button>
                            <a-input-number
                                v-model:value="currentPageId"
                                :min="1"
                                :max="totalPages"
                                size="small"
                                style="width: 80px"
                                @change="jumpToPage"
                            />
                            <a-button @click="nextPage" :disabled="currentPageId >= totalPages">
                                <template #icon><RightOutlined /></template>
                            </a-button>
                        </a-button-group>
                        <span class="page-total">/ {{ totalPages }}</span>
                    </div>
                </div>

                <div class="toolbar-right">
                    <a-button
                        type="text"
                        size="small"
                        @click="toggleFullscreen"
                        title="全屏"
                    >
                        <template #icon>
                            <FullscreenExitOutlined v-if="isFullscreen" />
                            <FullscreenOutlined v-else />
                        </template>
                    </a-button>
                </div>
            </div>

            <!-- 文档内容区域 -->
            <div class="document-viewer" ref="documentViewerRef">
                <div
                    class="document-container"
                    :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }"
                >
                    <div
                        v-for="page in visiblePages"
                        :key="page.page_id"
                        class="page-container"
                        :class="{ 'current-page': currentPageId === page.page_id }"
                        :data-page-id="page.page_id"
                        ref="pageRefs"
                    >
                        <div class="page-header">
                            <span class="page-number">第 {{ page.page_id }} 页</span>
                        </div>

                        <div class="page-content" :style="{ width: page.width + 'px', height: page.height + 'px' }">
                            <!-- 页面背景图片 -->
                            <img
                                v-if="page.base64"
                                :src="`data:image/jpeg;base64,${page.base64}`"
                                class="page-background"
                                :style="{ width: page.width + 'px', height: page.height + 'px' }"
                                @load="onPageImageLoad(page.page_id)"
                            />

                            <!-- 文本内容层 -->
                            <div class="text-layer">
                                <div
                                    v-for="content in getPageContent(page.page_id)"
                                    :key="`${content.page_id}-${content.paragraph_id}`"
                                    class="content-item"
                                    :class="[
                                        `type-${content.type}`,
                                        `subtype-${content.sub_type}`,
                                        {
                                            highlighted: highlightedItems.has(`${content.page_id}-${content.paragraph_id}`),
                                            'cross-page': content.split_section_page_ids?.length > 1
                                        }
                                    ]"
                                    :style="getContentStyle(content)"
                                    @click="selectContent(content)"
                                >
                                    <!-- 普通段落 -->
                                    <div v-if="content.type === 'paragraph'" class="paragraph-content">
                                        {{ content.text }}
                                    </div>

                                    <!-- 表格内容 -->
                                    <div v-else-if="content.type === 'table'" class="table-content">
                                        <table v-if="content.cells?.length" class="document-table">
                                            <tr v-for="row in getTableRows(content.cells)" :key="row.rowIndex">
                                                <td
                                                    v-for="cell in row.cells"
                                                    :key="`${cell.row}-${cell.col}`"
                                                    :rowspan="cell.row_span"
                                                    :colspan="cell.col_span"
                                                    :style="getCellStyle(cell)"
                                                    class="table-cell"
                                                >
                                                    {{ cell.text }}
                                                </td>
                                            </tr>
                                        </table>
                                        <div v-else class="table-placeholder">
                                            {{ content.text || '表格内容' }}
                                        </div>
                                    </div>

                                    <!-- 图片内容 -->
                                    <div v-else-if="content.type === 'image'" class="image-content">
                                        <img
                                            v-if="content.image_url"
                                            :src="content.image_url"
                                            class="content-image"
                                            @load="onContentImageLoad"
                                        />
                                        <div v-else class="image-placeholder">
                                            {{ content.text || '图片内容' }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 高亮遮罩层 -->
                            <div class="highlight-layer">
                                <div
                                    v-for="highlight in getPageHighlights(page.page_id)"
                                    :key="highlight.id"
                                    class="highlight-overlay"
                                    :style="getHighlightStyle(highlight)"
                                    :class="`highlight-${highlight.type}`"
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 修订建议面板 -->
        <div class="revision-panel" :class="{ collapsed: revisionPanelCollapsed }">
            <div class="revision-header">
                <h3>修订建议</h3>
                <div class="header-actions">
                    <a-button
                        type="text"
                        size="small"
                        @click="clearAllHighlights"
                        title="清除高亮"
                    >
                        <template #icon>
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                            </svg>
                        </template>
                    </a-button>
                    <a-button
                        type="text"
                        size="small"
                        @click="revisionPanelCollapsed = !revisionPanelCollapsed"
                        title="收起面板"
                    >
                        <template #icon>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                            </svg>
                        </template>
                    </a-button>
                </div>
            </div>

            <div class="revision-content" v-if="!revisionPanelCollapsed">
                <div class="revision-stats">
                    <a-statistic-countdown
                        title="待处理建议"
                        :value="revisionSuggestions.length"
                        format="D"
                    />
                </div>

                <div class="revision-filters">
                    <a-radio-group v-model:value="selectedRevisionType" size="small">
                        <a-radio-button value="all">全部</a-radio-button>
                        <a-radio-button value="add">新增</a-radio-button>
                        <a-radio-button value="delete">删除</a-radio-button>
                        <a-radio-button value="modify">修改</a-radio-button>
                    </a-radio-group>
                </div>

                <div class="revision-list">
                    <div
                        v-for="suggestion in filteredRevisionSuggestions"
                        :key="suggestion.id"
                        class="revision-item"
                        :class="[
                            `type-${suggestion.type}`,
                            { active: activeSuggestionId === suggestion.id }
                        ]" 
                    >
                        <div class="revision-item-header">
                            <a-tag 
                                size="small"
                            > 
                            </a-tag>
                            <span class="page-info">第{{ suggestion.pageId }}页</span>
                        </div>

                        <div class="revision-item-content">
                            <div v-if="suggestion.type === 'delete'" class="original-text">
                                <span class="label">删除内容：</span>
                                <span class="text deleted">{{ suggestion.originalText }}</span>
                            </div>

                            <div v-else-if="suggestion.type === 'add'" class="suggested-text">
                                <span class="label">新增内容：</span>
                                <span class="text added">{{ suggestion.suggestedText }}</span>
                            </div>

                            <div v-else-if="suggestion.type === 'modify'">
                                <div class="original-text">
                                    <span class="label">原文：</span>
                                    <span class="text deleted">{{ suggestion.originalText }}</span>
                                </div>
                                <div class="suggested-text">
                                    <span class="label">建议：</span>
                                    <span class="text added">{{ suggestion.suggestedText }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="revision-item-actions">
                            <a-button-group size="small">
                                <a-button
                                    type="primary"
                                    @click.stop="acceptSuggestion(suggestion)"
                                >
                                    接受
                                </a-button>
                                <a-button @click.stop="rejectSuggestion(suggestion)">
                                    拒绝
                                </a-button>
                            </a-button-group>
                        </div>

                        <div class="revision-item-reason" v-if="suggestion.reason">
                            <span class="label">建议理由：</span>
                            <span class="reason">{{ suggestion.reason }}</span>
                        </div>
                    </div>
                </div>

                <!-- 建议输入区域 -->
                <div class="suggestion-input">
                    <h4>添加建议</h4>
                    <a-textarea
                        v-model:value="newSuggestionText"
                        placeholder="请输入修订建议..."
                        :rows="3"
                    />
                    <div class="input-actions">
                        <a-button
                            type="primary"
                            size="small"
                            @click="addSuggestion"
                            :disabled="!newSuggestionText.trim()"
                        >
                            添加建议
                        </a-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { taskMarkDownDetail } from '@/api/examine'
import {
    MenuOutlined,
    MenuFoldOutlined,
    SearchOutlined,
    ZoomInOutlined,
    ZoomOutOutlined,
    LeftOutlined,
    RightOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined
} from '@ant-design/icons-vue'

defineOptions({
    name: 'DocumentReader'
})

// 类型定义
interface DocumentItem {
    page_id: number
    paragraph_id: number
    outline_level: number
    text: string
    type: 'paragraph' | 'table' | 'image'
    position: number[]
    origin_position: number[]
    tags: string[]
    sub_type?: string
    cells?: TableCell[]
    image_url?: string
    split_section_page_ids?: number[]
    split_section_positions?: number[][]
}

interface TableCell {
    col: number
    col_span: number
    position: number[]
    origin_position: number[]
    row: number
    row_span: number
    text: string
    type: string
}

interface PageData {
    page_id: number
    width: number
    height: number
    base64?: string
    origin_base64?: string
    angle: number
    content: any[]
    raw_ocr: any[]
    structured: any[]
}

interface TocItem {
    hierarchy: number
    title: string
    page_id: number
    pos: number[]
}

interface RevisionSuggestion {
    id: string
    type: 'add' | 'delete' | 'modify'
    pageId: number
    paragraphId: number
    position: {
        x1: number
        y1: number
        x2: number
        y2: number
    }
    originalText?: string
    suggestedText?: string
    reason?: string
    timestamp: number
    status: 'pending' | 'accepted' | 'rejected'
}

interface HighlightItem {
    id: string
    type: 'suggestion' | 'search' | 'selection'
    pageId: number
    position: {
        x1: number
        y1: number
        x2: number
        y2: number
    }
    color?: string
}

// 响应式数据
const documentData = ref<DocumentItem[]>([])
const pagesData = ref<any[]>([])
const tocData = ref<TocItem[]>([])
const loading = ref(true)

// 布局相关
const sidebarCollapsed = ref(false)
const documentViewerRef = ref<HTMLElement>()
const pageRefs = ref<HTMLElement[]>([])

// 缩放和导航
const zoomLevel = ref(1)
const currentPageId = ref(1)
const totalPages = ref(0)
const isFullscreen = ref(false)

// 目录搜索
const tocSearchText = ref('')
const filteredTocItems = computed(() => {
    if (!tocSearchText.value.trim()) {
        return tocData.value
    }
    return tocData.value.filter(item =>
        item.title.toLowerCase().includes(tocSearchText.value.toLowerCase())
    )
})

// 虚拟滚动优化 - 只渲染可见页面
const visiblePages = computed(() => {
    // 简化版本：渲染当前页面及前后各2页
    const start = Math.max(1, currentPageId.value - 2)
    const end = Math.min(totalPages.value, currentPageId.value + 2)
    return pagesData.value.filter(page =>
        page.page_id >= start && page.page_id <= end
    )
})

// 高亮相关
const highlightedItems = ref(new Set<string>())
const highlights = ref<HighlightItem[]>([])

// 修订建议相关
const revisionPanelCollapsed = ref(false)
const revisionSuggestions = ref<RevisionSuggestion[]>([])
const selectedRevisionType = ref<'all' | 'add' | 'delete' | 'modify'>('all')
const activeSuggestionId = ref<string>('')
const newSuggestionText = ref('')
const filteredRevisionSuggestions = computed(() => {
    if (selectedRevisionType.value === 'all') {
        return revisionSuggestions.value.filter(s => s.status === 'pending')
    }
    return revisionSuggestions.value.filter(s =>
        s.type === selectedRevisionType.value && s.status === 'pending'
    )
})

// 核心方法
// 缩放控制
const zoomIn = () => {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.25)
}

const zoomOut = () => {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.25)
}

const resetZoom = () => {
    zoomLevel.value = 1
}

// 页面导航
const jumpToPage = (pageId: number, position?: number[]) => {
    currentPageId.value = pageId
    nextTick(() => {
        const pageElement = document.querySelector(`[data-page-id="${pageId}"]`)
        if (pageElement) {
            pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })

            // 如果有具体位置，进一步滚动到该位置
            if (position && position.length >= 4) {
                const [x1, y1] = position
                const rect = pageElement.getBoundingClientRect()
                const targetY = rect.top + y1 * zoomLevel.value
                window.scrollTo({
                    top: targetY - 100, // 留出一些顶部空间
                    behavior: 'smooth'
                })
            }
        }
    })
}

const prevPage = () => {
    if (currentPageId.value > 1) {
        jumpToPage(currentPageId.value - 1)
    }
}

const nextPage = () => {
    if (currentPageId.value < totalPages.value) {
        jumpToPage(currentPageId.value + 1)
    }
}

// 全屏控制
const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
        documentViewerRef.value?.requestFullscreen()
        isFullscreen.value = true
    } else {
        document.exitFullscreen()
        isFullscreen.value = false
    }
}

// 内容获取和样式计算
const getPageContent = (pageId: number) => {
    return documentData.value.filter(item => item.page_id === pageId)
}

const getContentStyle = (content: DocumentItem) => {
    if (!content.position || content.position.length < 4) return {}

    const [x1, y1, x2, y2] = content.position
    return {
        position: 'absolute',
        left: `${x1}px`,
        top: `${y1}px`,
        width: `${x2 - x1}px`,
        height: `${y2 - y1}px`,
        fontSize: `${Math.max(12, (y2 - y1) * 0.8)}px`,
        lineHeight: '1.2'
    }
}

// 表格处理
const getTableRows = (cells: TableCell[]) => {
    if (!cells?.length) return []

    const rowMap = new Map<number, TableCell[]>()
    cells.forEach(cell => {
        if (!rowMap.has(cell.row)) {
            rowMap.set(cell.row, [])
        }
        rowMap.get(cell.row)!.push(cell)
    })

    return Array.from(rowMap.entries())
        .sort(([a], [b]) => a - b)
        .map(([rowIndex, cells]) => ({
            rowIndex,
            cells: cells.sort((a, b) => a.col - b.col)
        }))
}

const getCellStyle = (cell: TableCell) => {
    if (!cell.position || cell.position.length < 4) return {}

    const [x1, y1, x2, y2] = cell.position
    return {
        position: 'relative',
        minWidth: `${x2 - x1}px`,
        minHeight: `${y2 - y1}px`
    }
}

// 数据加载
const loadDocumentData = async () => {
    loading.value = true
    try {
        const {data, err} = await taskMarkDownDetail({taskId:'1955823962696978434'})
        if (err) {
            console.error('加载文档数据失败:', err)
            return
        }

        if (data.result) {
            // 解析文档详情
            if (data.result.detail) {
                documentData.value = data.result.detail
            }

            // 解析页面数据
            if (data.result.pages) {
                pagesData.value = data.result.pages
                totalPages.value = data.result.total_page_number || data.result.pages.length
            }

            // 解析目录数据
            if (data.result.catalog?.toc) {
                tocData.value = data.result.catalog.toc.flat()
            }
        }
    } catch (error) {
        console.error('加载文档数据异常:', error)
    } finally {
        loading.value = false
    }
}

// 高亮和选择
const selectContent = (content: DocumentItem) => {
    const key = `${content.page_id}-${content.paragraph_id}`
    if (highlightedItems.value.has(key)) {
        highlightedItems.value.delete(key)
    } else {
        highlightedItems.value.add(key)
    }
}

const getPageHighlights = (pageId: number) => {
    return highlights.value.filter(h => h.pageId === pageId)
}

const getHighlightStyle = (highlight: HighlightItem) => {
    return {
        position: 'absolute' as const,
        left: `${highlight.position.x1}px`,
        top: `${highlight.position.y1}px`,
        width: `${highlight.position.x2 - highlight.position.x1}px`,
        height: `${highlight.position.y2 - highlight.position.y1}px`,
        backgroundColor: highlight.color || 'rgba(255, 255, 0, 0.3)',
        pointerEvents: 'none' as const
    }
}

// 图片加载处理
const onPageImageLoad = (pageId: number) => {
    console.log(`页面 ${pageId} 图片加载完成`)
}

const onContentImageLoad = () => {
    console.log('内容图片加载完成')
}

// 建议处理
const jumpToSuggestion = (suggestion: RevisionSuggestion) => {
    activeSuggestionId.value = suggestion.id
    jumpToPage(suggestion.pageId)

    // 添加高亮
    const highlightId = `suggestion-${suggestion.id}`
    const existingIndex = highlights.value.findIndex(h => h.id === highlightId)

    const newHighlight: HighlightItem = {
        id: highlightId,
        type: 'suggestion',
        pageId: suggestion.pageId,
        position: suggestion.position,
        color: 'rgba(255, 165, 0, 0.4)'
    }

    if (existingIndex >= 0) {
        highlights.value[existingIndex] = newHighlight
    } else {
        highlights.value.push(newHighlight)
    }
}

const acceptSuggestion = (suggestion: RevisionSuggestion) => {
    // 更新建议状态
    const index = revisionSuggestions.value.findIndex(s => s.id === suggestion.id)
    if (index >= 0) {
        revisionSuggestions.value[index].status = 'accepted'

        // 应用修改到文档内容
        applyContentChange(suggestion)

        // 移除高亮
        const highlightIndex = highlights.value.findIndex(h => h.id === `suggestion-${suggestion.id}`)
        if (highlightIndex >= 0) {
            highlights.value.splice(highlightIndex, 1)
        }
    }
}

const rejectSuggestion = (suggestion: RevisionSuggestion) => {
    // 更新建议状态
    const index = revisionSuggestions.value.findIndex(s => s.id === suggestion.id)
    if (index >= 0) {
        revisionSuggestions.value[index].status = 'rejected'

        // 移除高亮
        const highlightIndex = highlights.value.findIndex(h => h.id === `suggestion-${suggestion.id}`)
        if (highlightIndex >= 0) {
            highlights.value.splice(highlightIndex, 1)
        }
    }
}

const applyContentChange = (suggestion: RevisionSuggestion) => {
    const contentIndex = documentData.value.findIndex(
        item => item.page_id === suggestion.pageId && item.paragraph_id === suggestion.paragraphId
    )

    if (contentIndex >= 0) {
        const content = documentData.value[contentIndex]

        switch (suggestion.type) {
            case 'modify':
                if (suggestion.suggestedText) {
                    content.text = suggestion.suggestedText
                }
                break
            case 'delete':
                documentData.value.splice(contentIndex, 1)
                break
            case 'add':
                // 添加新内容项
                if (suggestion.suggestedText) {
                    const newContent: DocumentItem = {
                        page_id: suggestion.pageId,
                        paragraph_id: Date.now(), // 临时ID
                        outline_level: -1,
                        text: suggestion.suggestedText,
                        type: 'paragraph',
                        position: [
                            suggestion.position.x1,
                            suggestion.position.y1,
                            suggestion.position.x2,
                            suggestion.position.y2
                        ],
                        origin_position: [
                            suggestion.position.x1,
                            suggestion.position.y1,
                            suggestion.position.x2,
                            suggestion.position.y2
                        ],
                        tags: []
                    }
                    documentData.value.push(newContent)
                }
                break
        }
    }
}

const addSuggestion = () => {
    if (!newSuggestionText.value.trim()) return

    const newSuggestion: RevisionSuggestion = {
        id: `suggestion-${Date.now()}`,
        type: 'add',
        pageId: currentPageId.value,
        paragraphId: 0,
        position: { x1: 100, y1: 100, x2: 400, y2: 150 },
        suggestedText: newSuggestionText.value.trim(),
        reason: '用户添加的建议',
        timestamp: Date.now(),
        status: 'pending'
    }

    revisionSuggestions.value.push(newSuggestion)
    newSuggestionText.value = ''
}

// 清除所有高亮
const clearAllHighlights = () => {
    highlightedItems.value.clear()
    highlights.value = []
    activeSuggestionId.value = ''
}

// 加载修订建议数据
const loadRevisionSuggestions = () => {
    // 模拟修订建议数据
    const mockSuggestions: RevisionSuggestion[] = [
        {
            id: '1',
            type: 'modify',
            pageId: 1,
            paragraphId: 2,
            position: { x1: 467, y1: 1154, x2: 1059, y2: 1203 },
            originalText: '项目编号：SZDL2025001316',
            suggestedText: '项目编号：SZDL2025001316（已更新）',
            reason: '项目编号需要更新为最新版本',
            timestamp: Date.now() - 3600000,
            status: 'pending'
        },
        {
            id: '2',
            type: 'delete',
            pageId: 2,
            paragraphId: 1,
            position: { x1: 224, y1: 437, x2: 1578, y2: 1059 },
            originalText: '资格性审查表中的第1项内容',
            reason: '此项内容重复，建议删除',
            timestamp: Date.now() - 1800000,
            status: 'pending'
        },
        {
            id: '3',
            type: 'add',
            pageId: 5,
            paragraphId: 3,
            position: { x1: 323, y1: 816, x2: 632, y2: 860 },
            suggestedText: '注意：价格分计算方法仅适用于本次招标项目。',
            reason: '需要添加价格计算方法的适用范围说明',
            timestamp: Date.now() - 900000,
            status: 'pending'
        }
    ]

    revisionSuggestions.value = mockSuggestions
}

// 清除所有高亮
const clearAllHighlights = () => {
    console.log('已清除所有高亮')
}
loadDocumentData()
loadRevisionSuggestions()
</script>

<style lang="scss" scoped>
.md-reader-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;

    .sidebar {
        width: 300px;
        background: white;
        border-right: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        transition: width 0.3s ease;

        &.collapsed {
            width: 48px;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }
        }

        .toc-container {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;

            .toc-item {
                padding: 8px 16px;
                cursor: pointer;
                border-left: 3px solid transparent;
                transition: all 0.2s ease;
                font-size: 14px;
                line-height: 1.4;

                &:hover {
                    background: #f0f0f0;
                }

                &.active {
                    background: #e6f7ff;
                    border-left-color: #1890ff;
                    color: #1890ff;
                }

                &.level-0 {
                    font-weight: 600;
                    font-size: 16px;
                    color: #1890ff;
                }

                &.level-1 {
                    padding-left: 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column; 
    }
}
 
.color-picker {
    position: fixed;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    z-index: 1000;

    .color-options {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;

        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;

            &:hover {
                border-color: #1890ff;
                transform: scale(1.1);
            }
        }
    }
}

.revision-panel {
    width: 400px;
    background: white;
    border-left: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;

    &.collapsed {
        width: 0;
        overflow: hidden;
    }

    .revision-header {
        padding: 16px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 4px;
        }
    }

    .revision-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .revision-stats {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .revision-filters {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .revision-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;

            .revision-item {
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                margin-bottom: 12px;
                padding: 12px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    border-color: #1890ff;
                    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
                }

                &.active {
                    border-color: #1890ff;
                    background: #e6f7ff;
                }

                &.type-add {
                    border-left: 4px solid #52c41a;
                }

                &.type-delete {
                    border-left: 4px solid #ff4d4f;
                }

                &.type-modify {
                    border-left: 4px solid #fa8c16;
                }

                .revision-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;

                    .page-info {
                        font-size: 12px;
                        color: #666;
                    }
                }

                .revision-item-content {
                    margin-bottom: 12px;

                    .label {
                        font-size: 12px;
                        color: #666;
                        font-weight: 500;
                    }

                    .text {
                        font-size: 13px;
                        line-height: 1.4;
                        margin-left: 4px;

                        &.deleted {
                            background: #fff2f0;
                            color: #cf1322;
                            text-decoration: line-through;
                            padding: 2px 4px;
                            border-radius: 2px;
                        }

                        &.added {
                            background: #f6ffed;
                            color: #389e0d;
                            padding: 2px 4px;
                            border-radius: 2px;
                        }
                    }

                    .original-text,
                    .suggested-text {
                        margin-bottom: 4px;
                    }
                }

                .revision-item-actions {
                    margin-bottom: 8px;
                }

                .revision-item-reason {
                    font-size: 12px;
                    color: #666;
                    background: #fafafa;
                    padding: 6px 8px;
                    border-radius: 4px;

                    .label {
                        font-weight: 500;
                    }

                    .reason {
                        margin-left: 4px;
                    }
                }
            }
        }

        .suggestion-input {
            border-top: 1px solid #e8e8e8;
            padding: 16px;

            h4 {
                margin: 0 0 8px 0;
                font-size: 14px;
                font-weight: 600;
            }

            .input-actions {
                margin-top: 8px;
                text-align: right;
            }
        }
    }
}

// 建议高亮样式
.suggestion-highlight {
    background: #fff7e6 !important;
    border: 2px solid #ffa940 !important;
    border-radius: 4px;
    animation: highlight-pulse 0.5s ease-in-out;
}

@keyframes highlight-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.help-content {
    padding: 8px;
    min-width: 200px;

    h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
    }

    p {
        margin: 4px 0;
        font-size: 12px;
        color: #666;
        line-height: 1.4;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .md-reader-container {
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(-100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .revision-panel {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .main-content {
            margin-left: 0;
        }
    }

    .page-container {
        margin: 8px;

        .page-content {
            padding: 16px;
        }
    }
}

// 打印样式
@media print {
    .md-reader-container {
        .sidebar,
        .toolbar,
        .revision-panel {
            display: none;
        }

        .main-content {
            margin: 0;
        }

        .page-container {
            box-shadow: none;
            margin: 0;
            page-break-inside: avoid;
        }

        .suggestion-highlight {
            background: transparent !important;
            border: none !important;
        }
    }
}

/* 内容很少的页面样式 */
.page-content.minimal-content {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.minimal-page-notice {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.page-number-display {
    font-size: 48px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 16px;
}

.page-type-hint {
    font-size: 14px;
    color: #999;
    background: #f5f5f5;
    padding: 4px 12px;
    border-radius: 12px;
}

.content-item.page-number-only {
    display: none; /* 隐藏单独的页码数字，因为已经在上面显示了 */
}
</style>
