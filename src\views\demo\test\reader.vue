<template>
    <div class="md-reader-container">
        <!-- 目录导航 -->
        <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
            <div class="sidebar-header">
                <h3>目录</h3>
                <a-button
                    type="text"
                    size="small"
                    @click="sidebarCollapsed = !sidebarCollapsed"
                >
                    <template #icon>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </template>
                </a-button> 
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <div class="toolbar"> </div>
            <!-- 文档内容区域 -->

        </div> 
        <!-- 修订建议面板 -->
        <div class="revision-panel" :class="{ collapsed: revisionPanelCollapsed }">
            <div class="revision-header">
                <h3>修订建议</h3>
                <div class="header-actions">
                    <a-button
                        type="text"
                        size="small"
                        @click="clearAllHighlights"
                        title="清除高亮"
                    >
                        <template #icon>
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                            </svg>
                        </template>
                    </a-button>
                    <a-button
                        type="text"
                        size="small"
                        @click="revisionPanelCollapsed = !revisionPanelCollapsed"
                        title="收起面板"
                    >
                        <template #icon>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                            </svg>
                        </template>
                    </a-button>
                </div>
            </div>

            <div class="revision-content" v-if="!revisionPanelCollapsed">
                <div class="revision-stats">
                    <a-statistic-countdown
                        title="待处理建议"
                        :value="revisionSuggestions.length"
                        format="D"
                    />
                </div>

                <div class="revision-filters">
                    <a-radio-group v-model:value="selectedRevisionType" size="small">
                        <a-radio-button value="all">全部</a-radio-button>
                        <a-radio-button value="add">新增</a-radio-button>
                        <a-radio-button value="delete">删除</a-radio-button>
                        <a-radio-button value="modify">修改</a-radio-button>
                    </a-radio-group>
                </div>

                <div class="revision-list">
                    <div
                        v-for="suggestion in filteredRevisionSuggestions"
                        :key="suggestion.id"
                        class="revision-item"
                        :class="[
                            `type-${suggestion.type}`,
                            { active: activeSuggestionId === suggestion.id }
                        ]" 
                    >
                        <div class="revision-item-header">
                            <a-tag 
                                size="small"
                            > 
                            </a-tag>
                            <span class="page-info">第{{ suggestion.pageId }}页</span>
                        </div>

                        <div class="revision-item-content">
                            <div v-if="suggestion.type === 'delete'" class="original-text">
                                <span class="label">删除内容：</span>
                                <span class="text deleted">{{ suggestion.originalText }}</span>
                            </div>

                            <div v-else-if="suggestion.type === 'add'" class="suggested-text">
                                <span class="label">新增内容：</span>
                                <span class="text added">{{ suggestion.suggestedText }}</span>
                            </div>

                            <div v-else-if="suggestion.type === 'modify'">
                                <div class="original-text">
                                    <span class="label">原文：</span>
                                    <span class="text deleted">{{ suggestion.originalText }}</span>
                                </div>
                                <div class="suggested-text">
                                    <span class="label">建议：</span>
                                    <span class="text added">{{ suggestion.suggestedText }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="revision-item-actions">
                            <a-button-group size="small">
                                <a-button
                                    type="primary"
                                    @click.stop="acceptSuggestion(suggestion)"
                                >
                                    接受
                                </a-button>
                                <a-button @click.stop="rejectSuggestion(suggestion)">
                                    拒绝
                                </a-button>
                            </a-button-group>
                        </div>

                        <div class="revision-item-reason" v-if="suggestion.reason">
                            <span class="label">建议理由：</span>
                            <span class="reason">{{ suggestion.reason }}</span>
                        </div>
                    </div>
                </div>

                <!-- 建议输入区域 -->
                <div class="suggestion-input">
                    <h4>添加建议</h4>
                    <a-textarea
                        v-model:value="newSuggestionText"
                        placeholder="请输入修订建议..."
                        :rows="3"
                    />
                    <div class="input-actions">
                        <a-button
                            type="primary"
                            size="small"
                            @click="addSuggestion"
                            :disabled="!newSuggestionText.trim()"
                        >
                            添加建议
                        </a-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { taskMarkDownDetail } from '@/api/examine'
defineOptions({
    name: 'MdReaderIndex'
})

// 类型定义
interface DocumentItem {
    page_id: number
    paragraph_id: number
    outline_level: string
    text: string
    type: 'paragraph' | 'table'
    position: string
    tags: string[]
    sub_type?: string
    cells?: TableCell[]
}

interface TableCell {
    col: number
    col_span: number
    position: number[]
    row: number
    row_span: number
    text: string
    type: 'cell'
}

interface RevisionSuggestion {
    id: string
    type: 'add' | 'delete' | 'modify'
    pageId: number
    paragraphId: number
    position: {
        x1: number
        y1: number
        x2: number
        y2: number
    }
    originalText?: string
    suggestedText?: string
    reason?: string
    timestamp: number
    status: 'pending' | 'accepted' | 'rejected'
}

// 响应式数据
const documentData = ref<DocumentItem[]>([])
const loading = ref(true)

// 布局相关
const sidebarCollapsed = ref(false)

// 修订建议相关
const revisionPanelCollapsed = ref(false)
const revisionSuggestions = ref<RevisionSuggestion[]>([])
const selectedRevisionType = ref<'all' | 'add' | 'delete' | 'modify'>('all')
const activeSuggestionId = ref<string>('')
const newSuggestionText = ref('') 
const filteredRevisionSuggestions = computed(() => {
    if (selectedRevisionType.value === 'all') {
        return revisionSuggestions.value.filter(s => s.status === 'pending')
    }
    return revisionSuggestions.value.filter(s =>
        s.type === selectedRevisionType.value && s.status === 'pending'
    )
})

// 数据加载
const loadDocumentData = async () => { 
    loading.value = true 
    const {data,err} = await taskMarkDownDetail({taskId:'1955823962696978434'})
    loading.value = false
    if(err) return 
    if(data.markDownDetail) documentData.value = JSON.parse(data.markDownDetail) 
}

const acceptSuggestion = (suggestion: RevisionSuggestion) => {
    console.log('接受建议:', suggestion)
}

const rejectSuggestion = (suggestion: RevisionSuggestion) => {
    // 更新建议状态
    console.log('拒绝建议:', suggestion)
}

const addSuggestion = () => {
    console.log('添加建议:')
}

// 加载修订建议数据
const loadRevisionSuggestions = () => {
    // 模拟修订建议数据
    const mockSuggestions: RevisionSuggestion[] = [
        {
            id: '1',
            type: 'modify',
            pageId: 1,
            paragraphId: 2,
            position: { x1: 467, y1: 1154, x2: 1059, y2: 1203 },
            originalText: '项目编号：SZDL2025001316',
            suggestedText: '项目编号：SZDL2025001316（已更新）',
            reason: '项目编号需要更新为最新版本',
            timestamp: Date.now() - 3600000,
            status: 'pending'
        },
        {
            id: '2',
            type: 'delete',
            pageId: 2,
            paragraphId: 1,
            position: { x1: 224, y1: 437, x2: 1578, y2: 1059 },
            originalText: '资格性审查表中的第1项内容',
            reason: '此项内容重复，建议删除',
            timestamp: Date.now() - 1800000,
            status: 'pending'
        },
        {
            id: '3',
            type: 'add',
            pageId: 5,
            paragraphId: 3,
            position: { x1: 323, y1: 816, x2: 632, y2: 860 },
            suggestedText: '注意：价格分计算方法仅适用于本次招标项目。',
            reason: '需要添加价格计算方法的适用范围说明',
            timestamp: Date.now() - 900000,
            status: 'pending'
        }
    ]

    revisionSuggestions.value = mockSuggestions
}

// 清除所有高亮
const clearAllHighlights = () => {
    console.log('已清除所有高亮')
}
loadDocumentData()
loadRevisionSuggestions()
</script>

<style lang="scss" scoped>
.md-reader-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;

    .sidebar {
        width: 300px;
        background: white;
        border-right: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        transition: width 0.3s ease;

        &.collapsed {
            width: 48px;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }
        }

        .toc-container {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;

            .toc-item {
                padding: 8px 16px;
                cursor: pointer;
                border-left: 3px solid transparent;
                transition: all 0.2s ease;
                font-size: 14px;
                line-height: 1.4;

                &:hover {
                    background: #f0f0f0;
                }

                &.active {
                    background: #e6f7ff;
                    border-left-color: #1890ff;
                    color: #1890ff;
                }

                &.level-0 {
                    font-weight: 600;
                    font-size: 16px;
                    color: #1890ff;
                }

                &.level-1 {
                    padding-left: 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column; 
    }
}
 
.color-picker {
    position: fixed;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    z-index: 1000;

    .color-options {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;

        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;

            &:hover {
                border-color: #1890ff;
                transform: scale(1.1);
            }
        }
    }
}

.revision-panel {
    width: 400px;
    background: white;
    border-left: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;

    &.collapsed {
        width: 0;
        overflow: hidden;
    }

    .revision-header {
        padding: 16px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 4px;
        }
    }

    .revision-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .revision-stats {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .revision-filters {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .revision-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;

            .revision-item {
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                margin-bottom: 12px;
                padding: 12px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    border-color: #1890ff;
                    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
                }

                &.active {
                    border-color: #1890ff;
                    background: #e6f7ff;
                }

                &.type-add {
                    border-left: 4px solid #52c41a;
                }

                &.type-delete {
                    border-left: 4px solid #ff4d4f;
                }

                &.type-modify {
                    border-left: 4px solid #fa8c16;
                }

                .revision-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;

                    .page-info {
                        font-size: 12px;
                        color: #666;
                    }
                }

                .revision-item-content {
                    margin-bottom: 12px;

                    .label {
                        font-size: 12px;
                        color: #666;
                        font-weight: 500;
                    }

                    .text {
                        font-size: 13px;
                        line-height: 1.4;
                        margin-left: 4px;

                        &.deleted {
                            background: #fff2f0;
                            color: #cf1322;
                            text-decoration: line-through;
                            padding: 2px 4px;
                            border-radius: 2px;
                        }

                        &.added {
                            background: #f6ffed;
                            color: #389e0d;
                            padding: 2px 4px;
                            border-radius: 2px;
                        }
                    }

                    .original-text,
                    .suggested-text {
                        margin-bottom: 4px;
                    }
                }

                .revision-item-actions {
                    margin-bottom: 8px;
                }

                .revision-item-reason {
                    font-size: 12px;
                    color: #666;
                    background: #fafafa;
                    padding: 6px 8px;
                    border-radius: 4px;

                    .label {
                        font-weight: 500;
                    }

                    .reason {
                        margin-left: 4px;
                    }
                }
            }
        }

        .suggestion-input {
            border-top: 1px solid #e8e8e8;
            padding: 16px;

            h4 {
                margin: 0 0 8px 0;
                font-size: 14px;
                font-weight: 600;
            }

            .input-actions {
                margin-top: 8px;
                text-align: right;
            }
        }
    }
}

// 建议高亮样式
.suggestion-highlight {
    background: #fff7e6 !important;
    border: 2px solid #ffa940 !important;
    border-radius: 4px;
    animation: highlight-pulse 0.5s ease-in-out;
}

@keyframes highlight-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.help-content {
    padding: 8px;
    min-width: 200px;

    h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
    }

    p {
        margin: 4px 0;
        font-size: 12px;
        color: #666;
        line-height: 1.4;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .md-reader-container {
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(-100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .revision-panel {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            transform: translateX(100%);
            transition: transform 0.3s ease;

            &:not(.collapsed) {
                transform: translateX(0);
            }
        }

        .main-content {
            margin-left: 0;
        }
    }

    .page-container {
        margin: 8px;

        .page-content {
            padding: 16px;
        }
    }
}

// 打印样式
@media print {
    .md-reader-container {
        .sidebar,
        .toolbar,
        .revision-panel {
            display: none;
        }

        .main-content {
            margin: 0;
        }

        .page-container {
            box-shadow: none;
            margin: 0;
            page-break-inside: avoid;
        }

        .suggestion-highlight {
            background: transparent !important;
            border: none !important;
        }
    }
}

/* 内容很少的页面样式 */
.page-content.minimal-content {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.minimal-page-notice {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.page-number-display {
    font-size: 48px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 16px;
}

.page-type-hint {
    font-size: 14px;
    color: #999;
    background: #f5f5f5;
    padding: 4px 12px;
    border-radius: 12px;
}

.content-item.page-number-only {
    display: none; /* 隐藏单独的页码数字，因为已经在上面显示了 */
}
</style>
